'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '../../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../../lib/translations';
import { addToCart as addToSessionCart } from '../../../../lib/session-cart';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';

interface ProductPageClientProps {
  initialProduct: ProductWithDetails | null;
  initialCategory: Category | null;
  initialSubcategory: Subcategory | null;
  locale: Locale;
  productId: string;
}

export default function ProductPageClient({
  initialProduct,
  initialCategory,
  initialSubcategory,
  locale,
  productId
}: ProductPageClientProps) {
  const router = useRouter();
  const [product, setProduct] = useState<ProductWithDetails | null>(initialProduct);
  const [category, setCategory] = useState<Category | null>(initialCategory);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(initialSubcategory);
  const [loading, setLoading] = useState(!initialProduct);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [whatsappSettings] = useState({
    businessNumber: '+966 599252259',
    welcomeMessage: 'Hello! How can we help you today?',
    welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
    enabled: true
  });

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  const fetchProductDetails = useCallback(async (productId: string) => {
    try {
      setLoading(true);
      console.log('🚀 Client: Fetching product details for ID:', productId);

      // إنشاء AbortController للتحكم في timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 ثانية

      // جلب تفاصيل المنتج مع معالجة أفضل للأخطاء
      const productResponse = await fetch(`/api/products/${productId}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('📡 Client: Product response status:', productResponse.status);

      if (!productResponse.ok) {
        console.error('❌ Client: Product API response not ok:', productResponse.status);
        if (productResponse.status === 404) {
          router.push(`/${locale}/products`);
          return;
        }
        throw new Error(`HTTP error! status: ${productResponse.status}`);
      }

      const productResult = await productResponse.json();
      console.log('📦 Client: Product API response:', productResult);

      if (productResult.success && productResult.data) {
        const productData = productResult.data;
        setProduct(productData);

        // جلب بيانات الفئة الرئيسية
        if (productData.category_id) {
          try {
            const categoriesResponse = await fetch(`/api/categories?id=${productData.category_id}`);
            if (categoriesResponse.ok) {
              const categoriesResult = await categoriesResponse.json();
              console.log('📦 استجابة API الفئة:', categoriesResult);

              if (categoriesResult.success && categoriesResult.data) {
                setCategory(categoriesResult.data);
              }
            }
          } catch (error) {
            console.error('❌ خطأ في جلب بيانات الفئة:', error);
          }
        }

        // جلب بيانات الفئة الفرعية
        if (productData.subcategory_id) {
          try {
            const subcategoriesResponse = await fetch(`/api/subcategories?id=${productData.subcategory_id}`);
            if (subcategoriesResponse.ok) {
              const subcategoriesResult = await subcategoriesResponse.json();
              console.log('📦 استجابة API الفئة الفرعية:', subcategoriesResult);

              if (subcategoriesResult.success && subcategoriesResult.data) {
                setSubcategory(subcategoriesResult.data);
              }
            }
          } catch (error) {
            console.error('❌ خطأ في جلب بيانات الفئة الفرعية:', error);
          }
        }
      } else {
        console.error('❌ Client: Invalid product data structure:', productResult);
        router.push(`/${locale}/products`);
      }
    } catch (error) {
      console.error('❌ Client: Error fetching product details:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('❌ Client: Request timeout');
      }
      // في حالة الخطأ، توجيه المستخدم إلى صفحة المنتجات
      router.push(`/${locale}/products`);
    } finally {
      setLoading(false);
    }
  }, [router, locale]);

  // إذا لم تكن البيانات الأولية متوفرة، جلبها من العميل
  useEffect(() => {
    if (!initialProduct && productId) {
      fetchProductDetails(productId);
    }
  }, [productId, initialProduct, fetchProductDetails]);

  const addToCart = () => {
    if (!product) return;

    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const cartItem = {
      id: product.id,
      title: productTitle,
      titleAr: product.title_ar,
      price: product.price,
      quantity: quantity,
      image: product.images && product.images.length > 0 ? product.images[0].image_url : '/placeholder-image.jpg'
    };

    addToSessionCart(cartItem);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  const sendWhatsAppMessage = () => {
    if (!product || !whatsappSettings.enabled) return;

    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const message = locale === 'ar'
      ? `مرحباً، أريد الاستفسار عن المنتج: ${productTitle}\nالرمز: ${product.id}\nالسعر: ${product.price} ريال`
      : `Hello, I want to inquire about the product: ${productTitle}\nCode: ${product.id}\nPrice: ${product.price} SAR`;

    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${whatsappSettings.businessNumber.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;

    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">{t('productDetails.loading')}</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('productDetails.notFound')}</h1>
          <p className="text-gray-600 mb-8">{t('productDetails.notFoundMessage')}</p>
          <button
            onClick={() => router.push(`/${locale}/products`)}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
          >
            {t('productDetails.backToProducts')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 mb-8">
        <a href={`/${locale}`} className="hover:text-primary transition-colors">
          {t('home')}
        </a>
        <span className="text-gray-400">/</span>
        <a href={`/${locale}/products`} className="hover:text-primary transition-colors">
          {t('products')}
        </a>
        {category && (
          <>
            <span className="text-gray-400">/</span>
            <span className="text-gray-500">{category.name}</span>
          </>
        )}
        {subcategory && (
          <>
            <span className="text-gray-400">/</span>
            <span className="text-gray-500">{subcategory.name}</span>
          </>
        )}
        <span className="text-gray-400">/</span>
        <span className="text-primary font-medium">{locale === 'ar' ? product.title_ar : product.title}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square bg-gray-100 rounded-2xl overflow-hidden shadow-lg">
            {product.images && product.images.length > 0 ? (
              <Image
                src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
                alt={locale === 'ar' ? product.title_ar : product.title}
                width={600}
                height={600}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                priority
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <i className="ri-image-line text-6xl"></i>
              </div>
            )}
          </div>

          {/* Thumbnail Images */}
          {product.images && product.images.length > 1 && (
            <div className="flex space-x-2 rtl:space-x-reverse overflow-x-auto">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImageIndex === index
                      ? 'border-primary shadow-lg'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image.image_url}
                    alt={`${locale === 'ar' ? product.title_ar : product.title} ${index + 1}`}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{locale === 'ar' ? product.title_ar : product.title}</h1>
            <p className="text-lg text-gray-600">{locale === 'ar' ? 'الرمز:' : 'Code:'} {product.id}</p>
          </div>

          {/* Price */}
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <span className="text-3xl font-bold text-primary">
                {product.price} {t('currency')}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                product.is_available
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {product.is_available ? t('available') : t('unavailable')}
              </span>
            </div>
          </div>

          {/* Description */}
          {(product.description || product.description_ar) && (
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{t('productDetails.description')}</h3>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {locale === 'ar' ? product.description_ar : product.description}
              </p>
            </div>
          )}

          {/* Features */}
          {product.features && product.features.length > 0 && (
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{t('productDetails.features')}</h3>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                    <i className="ri-check-line text-green-500 text-lg mt-0.5"></i>
                    <span className="text-gray-700">
                      {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Specifications */}
          {product.specifications && product.specifications.length > 0 && (
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{t('productDetails.technicalSpecs')}</h3>
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="grid grid-cols-1 gap-3">
                  {product.specifications.map((spec, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                      <span className="font-medium text-gray-700">{locale === 'ar' ? spec.spec_key_ar : spec.spec_key}</span>
                      <span className="text-gray-600">{locale === 'ar' ? spec.spec_value_ar : spec.spec_value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Quantity and Actions */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <label className="text-gray-700 font-medium">{t('productDetails.quantity')}</label>
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 text-gray-600 hover:text-primary transition-colors"
                >
                  -
                </button>
                <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">
                  {quantity}
                </span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 text-gray-600 hover:text-primary transition-colors"
                >
                  +
                </button>
              </div>
            </div>

            <div className="flex space-x-4 rtl:space-x-reverse">
              <button
                onClick={addToCart}
                disabled={!product.is_available}
                className="flex-1 bg-primary text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <i className="ri-shopping-cart-line"></i>
                <span>{t('productDetails.addToCart')}</span>
              </button>
              
              <button
                onClick={sendWhatsAppMessage}
                className="bg-green-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <i className="ri-whatsapp-line"></i>
                <span>{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-fade-in">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-check-line"></i>
            <span>{t('cartMessages.addedToCart')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
