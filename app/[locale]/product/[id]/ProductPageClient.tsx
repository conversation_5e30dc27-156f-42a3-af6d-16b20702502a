'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '../../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../../lib/translations';
import { addToCart as addToSessionCart } from '../../../../lib/session-cart';
import { ProductWithDetails, Category, Subcategory } from '../../../../types/mysql-database';
import MobileProductView from './MobileProductView';

interface ProductPageClientProps {
  initialProduct: ProductWithDetails | null;
  initialCategory: Category | null;
  initialSubcategory: Subcategory | null;
  locale: Locale;
  productId: string;
}

export default function ProductPageClient({
  initialProduct,
  initialCategory,
  initialSubcategory,
  locale,
  productId
}: ProductPageClientProps) {
  const router = useRouter();
  const [product, setProduct] = useState<ProductWithDetails | null>(initialProduct);
  const [category, setCategory] = useState<Category | null>(initialCategory);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(initialSubcategory);
  const [loading, setLoading] = useState(!initialProduct);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [activeTab, setActiveTab] = useState('description');
  const [isMobile, setIsMobile] = useState(false);
  const [whatsappSettings] = useState({
    businessNumber: '+966 599252259',
    welcomeMessage: 'Hello! How can we help you today?',
    welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
    enabled: true
  });

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  // Check if mobile screen
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const fetchProductDetails = useCallback(async (productId: string) => {
    try {
      setLoading(true);
      console.log('🚀 Client: Fetching product details for ID:', productId);

      // إنشاء AbortController للتحكم في timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 ثانية

      // جلب تفاصيل المنتج مع معالجة أفضل للأخطاء
      const productResponse = await fetch(`/api/products/${productId}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('📡 Client: Product response status:', productResponse.status);

      if (!productResponse.ok) {
        console.error('❌ Client: Product API response not ok:', productResponse.status);
        if (productResponse.status === 404) {
          router.push(`/${locale}/products`);
          return;
        }
        throw new Error(`HTTP error! status: ${productResponse.status}`);
      }

      const productResult = await productResponse.json();
      console.log('📦 Client: Product API response:', productResult);

      if (productResult.success && productResult.data) {
        const productData = productResult.data;
        setProduct(productData);

        // جلب بيانات الفئة الرئيسية
        if (productData.category_id) {
          try {
            const categoriesResponse = await fetch(`/api/categories?id=${productData.category_id}`);
            if (categoriesResponse.ok) {
              const categoriesResult = await categoriesResponse.json();
              console.log('📦 استجابة API الفئة:', categoriesResult);

              if (categoriesResult.success && categoriesResult.data) {
                setCategory(categoriesResult.data);
              }
            }
          } catch (error) {
            console.error('❌ خطأ في جلب بيانات الفئة:', error);
          }
        }

        // جلب بيانات الفئة الفرعية
        if (productData.subcategory_id) {
          try {
            const subcategoriesResponse = await fetch(`/api/subcategories?id=${productData.subcategory_id}`);
            if (subcategoriesResponse.ok) {
              const subcategoriesResult = await subcategoriesResponse.json();
              console.log('📦 استجابة API الفئة الفرعية:', subcategoriesResult);

              if (subcategoriesResult.success && subcategoriesResult.data) {
                setSubcategory(subcategoriesResult.data);
              }
            }
          } catch (error) {
            console.error('❌ خطأ في جلب بيانات الفئة الفرعية:', error);
          }
        }
      } else {
        console.error('❌ Client: Invalid product data structure:', productResult);
        router.push(`/${locale}/products`);
      }
    } catch (error) {
      console.error('❌ Client: Error fetching product details:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('❌ Client: Request timeout');
      }
      // في حالة الخطأ، توجيه المستخدم إلى صفحة المنتجات
      router.push(`/${locale}/products`);
    } finally {
      setLoading(false);
    }
  }, [router, locale]);

  // إذا لم تكن البيانات الأولية متوفرة، جلبها من العميل
  useEffect(() => {
    if (!initialProduct && productId) {
      fetchProductDetails(productId);
    }
  }, [productId, initialProduct, fetchProductDetails]);

  const addToCart = (qty?: number) => {
    if (!product) return;

    const quantityToAdd = qty || quantity;
    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const cartItem = {
      id: product.id,
      title: productTitle,
      titleAr: product.title_ar,
      price: product.price,
      quantity: quantityToAdd,
      image: product.images && product.images.length > 0 ? product.images[0].image_url : '/placeholder-image.jpg'
    };

    addToSessionCart(cartItem);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  const handleAddToCartClick = () => {
    addToCart();
  };

  const sendWhatsAppMessage = () => {
    if (!product || !whatsappSettings.enabled) return;

    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const message = locale === 'ar'
      ? `مرحباً، أريد الاستفسار عن المنتج: ${productTitle}\nالرمز: ${product.id}\nالسعر: ${product.price} ريال`
      : `Hello, I want to inquire about the product: ${productTitle}\nCode: ${product.id}\nPrice: ${product.price} SAR`;

    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${whatsappSettings.businessNumber.replace(/[^0-9]/g, '')}?text=${encodedMessage}`;

    window.open(whatsappUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">{t('productDetails.loading')}</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('productDetails.notFound')}</h1>
          <p className="text-gray-600 mb-8">{t('productDetails.notFoundMessage')}</p>
          <button
            onClick={() => router.push(`/${locale}/products`)}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
          >
            {t('productDetails.backToProducts')}
          </button>
        </div>
      </div>
    );
  }

  // Mobile view
  if (isMobile) {
    return (
      <MobileProductView
        product={product}
        category={category}
        locale={locale}
        onAddToCart={addToCart}
        onWhatsAppClick={sendWhatsAppMessage}
      />
    );
  }

  // Desktop view
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600 mb-8">
        <a href={`/${locale}`} className="hover:text-primary transition-colors">
          {t('home')}
        </a>
        <span className="text-gray-400">/</span>
        <a href={`/${locale}/products`} className="hover:text-primary transition-colors">
          {t('products')}
        </a>
        {category && (
          <>
            <span className="text-gray-400">/</span>
            <span className="text-gray-500">{category.name}</span>
          </>
        )}
        {subcategory && (
          <>
            <span className="text-gray-400">/</span>
            <span className="text-gray-500">{subcategory.name}</span>
          </>
        )}
        <span className="text-gray-400">/</span>
        <span className="text-primary font-medium">{locale === 'ar' ? product.title_ar : product.title}</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Product Images - مدمجة وعملية */}
        <div className="space-y-3">
          {/* Main Image - حجم متوسط */}
          <div className="relative group">
            <div className="aspect-square bg-gray-50 rounded-2xl overflow-hidden shadow-lg border border-gray-100 max-w-md mx-auto lg:max-w-none">
              {product.images && product.images.length > 0 ? (
                <Image
                  src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
                  alt={locale === 'ar' ? product.title_ar : product.title}
                  width={400}
                  height={400}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  priority
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <i className="ri-image-line text-4xl mb-1"></i>
                    <p className="text-sm">{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Image Counter Badge */}
            {product.images && product.images.length > 1 && (
              <div className="absolute top-3 right-3 bg-black/60 text-white px-2 py-1 rounded-lg text-xs font-medium">
                {selectedImageIndex + 1} / {product.images.length}
              </div>
            )}
          </div>

          {/* Thumbnail Images - مدمجة */}
          {product.images && product.images.length > 1 && (
            <div className="flex justify-center space-x-2 rtl:space-x-reverse overflow-x-auto">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImageIndex === index
                      ? 'border-primary shadow-md'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image.image_url}
                    alt={`${locale === 'ar' ? product.title_ar : product.title} ${index + 1}`}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Details - مدمجة وعملية */}
        <div className="space-y-4">
          {/* Header Section */}
          <div>
            <div className="flex items-start justify-between mb-2">
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">
                {locale === 'ar' ? product.title_ar : product.title}
              </h1>

              {/* Availability Badge */}
              <div className={`px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1 rtl:space-x-reverse ${
                product.is_available
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  product.is_available ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span>{product.is_available ? t('available') : t('unavailable')}</span>
              </div>
            </div>

            <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
              <span className="flex items-center space-x-1 rtl:space-x-reverse">
                <i className="ri-barcode-line"></i>
                <span>{locale === 'ar' ? 'الرمز:' : 'Code:'} {product.id}</span>
              </span>
              {category && (
                <span className="flex items-center space-x-1 rtl:space-x-reverse">
                  <i className="ri-folder-line"></i>
                  <span>{locale === 'ar' ? category.name_ar : category.name}</span>
                </span>
              )}
            </div>
          </div>

          {/* Price Section - مدمجة */}
          <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-1">
                  {locale === 'ar' ? 'السعر' : 'Price'}
                </p>
                <div className="flex items-baseline space-x-2 rtl:space-x-reverse">
                  <span className="text-2xl lg:text-3xl font-bold text-primary">
                    {product.price}
                  </span>
                  <span className="text-lg text-primary/70 font-medium">
                    {t('currency')}
                  </span>
                </div>
              </div>

              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                <i className="ri-price-tag-3-line text-xl text-primary"></i>
              </div>
            </div>
          </div>

          {/* Content Tabs - مدمجة */}
          <div className="space-y-3">
            {/* Tab Navigation */}
            <div className="flex space-x-1 rtl:space-x-reverse bg-gray-100 p-1 rounded-xl">
              {[
                { key: 'description', label: t('productDetails.description'), icon: 'ri-file-text-line' },
                { key: 'features', label: t('productDetails.features'), icon: 'ri-star-line' },
                { key: 'specs', label: t('productDetails.technicalSpecs'), icon: 'ri-settings-3-line' }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex-1 flex items-center justify-center space-x-1 rtl:space-x-reverse px-2 py-2 rounded-lg font-medium transition-all text-sm ${
                    activeTab === tab.key
                      ? 'bg-white text-primary shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <i className={`${tab.icon}`}></i>
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="min-h-[120px]">
              {/* Description Tab */}
              {activeTab === 'description' && (product.description || product.description_ar) && (
                <div className="bg-gray-50 rounded-xl p-4">
                  <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                    {locale === 'ar' ? product.description_ar : product.description}
                  </p>
                </div>
              )}

              {/* Features Tab */}
              {activeTab === 'features' && product.features && product.features.length > 0 && (
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="space-y-2">
                    {product.features.map((feature, index) => (
                      <div key={index} className="flex items-start space-x-2 rtl:space-x-reverse">
                        <div className="flex-shrink-0 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                          <i className="ri-check-line text-white text-xs"></i>
                        </div>
                        <span className="text-gray-700 text-sm">
                          {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Specifications Tab */}
              {activeTab === 'specs' && product.specifications && product.specifications.length > 0 && (
                <div className="bg-gray-50 rounded-xl p-4">
                  <div className="space-y-2">
                    {product.specifications.map((spec, index) => (
                      <div key={index} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                        <span className="font-medium text-gray-800 text-sm">
                          {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}
                        </span>
                        <span className="text-gray-600 text-sm">
                          {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quantity and Actions - مدمجة */}
          <div className="space-y-4">
            {/* Quantity Selector */}
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                {t('productDetails.quantity')}
              </label>
              <div className="flex items-center bg-gray-100 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-primary transition-colors rounded-l-lg"
                >
                  <i className="ri-subtract-line"></i>
                </button>
                <div className="w-12 h-8 flex items-center justify-center border-x border-gray-200 bg-white">
                  <span className="text-sm font-medium text-gray-800">{quantity}</span>
                </div>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-primary transition-colors rounded-r-lg"
                >
                  <i className="ri-add-line"></i>
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={handleAddToCartClick}
                disabled={!product.is_available}
                className="bg-primary text-white py-3 px-4 rounded-xl font-semibold hover:bg-primary-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <i className="ri-shopping-cart-line"></i>
                <span>{t('productDetails.addToCart')}</span>
              </button>

              <button
                onClick={sendWhatsAppMessage}
                className="bg-green-500 text-white py-3 px-4 rounded-xl font-semibold hover:bg-green-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
              >
                <i className="ri-whatsapp-line"></i>
                <span>{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Toast Notification - محسنة */}
      {showToast && (
        <div className="fixed bottom-6 right-6 bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-4 rounded-2xl shadow-2xl z-50 animate-fade-in border border-green-400">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <i className="ri-check-line text-lg font-bold"></i>
            </div>
            <span className="font-semibold text-lg">{t('cartMessages.addedToCart')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
