'use client';

import { useState } from 'react';
import Image from 'next/image';
import { ProductWithDetails, Category } from '../../../../types/mysql-database';
import { getTranslation } from '../../../../lib/translations';
import { Locale } from '../../../../lib/i18n';

interface MobileProductViewProps {
  product: ProductWithDetails;
  category: Category | null;
  locale: Locale;
  onAddToCart: (quantity: number) => void;
  onWhatsAppClick: () => void;
}

export default function MobileProductView({
  product,
  category,
  locale,
  onAddToCart,
  onWhatsAppClick
}: MobileProductViewProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');
  const [showToast, setShowToast] = useState(false);

  const t = (key: string) => getTranslation(locale, key as 'home' | 'products' | 'categories' | 'about' | 'contact' | 'cart' | 'admin' | 'loading' | 'search' | 'filter' | 'sort' | 'price' | 'currency' | 'available' | 'unavailable' | 'quantity' | 'addToCart' | 'viewDetails' | 'productDetails.title' | 'productDetails.description' | 'productDetails.features' | 'productDetails.specifications' | 'productDetails.addToCart' | 'productDetails.quantity' | 'productDetails.price' | 'productDetails.available' | 'productDetails.unavailable' | 'productDetails.notFound' | 'productDetails.notFoundMessage' | 'productDetails.backToProducts' | 'productDetails.loading' | 'productDetails.technicalSpecs' | 'cartMessages.addedToCart' | 'cartMessages.empty' | 'cartMessages.total' | 'cartMessages.checkout' | 'whatsapp.message');

  const handleAddToCart = () => {
    onAddToCart(quantity);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Mobile Header - مدمج */}
      <div className="sticky top-0 bg-white border-b border-gray-200 z-10 px-4 py-3">
        <div className="flex items-center justify-between">
          <button 
            onClick={() => window.history.back()}
            className="w-8 h-8 flex items-center justify-center rounded-lg bg-gray-100"
          >
            <i className="ri-arrow-left-line text-gray-600"></i>
          </button>
          <h1 className="text-sm font-semibold text-gray-900 truncate mx-3 flex-1">
            {locale === 'ar' ? product.title_ar : product.title}
          </h1>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${
            product.is_available
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {product.is_available ? t('available') : t('unavailable')}
          </div>
        </div>
      </div>

      {/* Product Images - محسنة للهاتف */}
      <div className="px-4 py-3">
        <div className="relative">
          <div className="aspect-square bg-gray-50 rounded-xl overflow-hidden">
            {product.images && product.images.length > 0 ? (
              <Image
                src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
                alt={locale === 'ar' ? product.title_ar : product.title}
                width={300}
                height={300}
                className="w-full h-full object-cover"
                priority
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <i className="ri-image-line text-3xl mb-1"></i>
                  <p className="text-xs">{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                </div>
              </div>
            )}
          </div>
          
          {/* Image Counter */}
          {product.images && product.images.length > 1 && (
            <div className="absolute top-2 right-2 bg-black/60 text-white px-2 py-1 rounded-lg text-xs">
              {selectedImageIndex + 1} / {product.images.length}
            </div>
          )}
        </div>

        {/* Thumbnail Images */}
        {product.images && product.images.length > 1 && (
          <div className="flex space-x-2 rtl:space-x-reverse mt-3 overflow-x-auto">
            {product.images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImageIndex(index)}
                className={`flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden border-2 ${
                  selectedImageIndex === index
                    ? 'border-primary'
                    : 'border-gray-200'
                }`}
              >
                <Image
                  src={image.image_url}
                  alt={`${locale === 'ar' ? product.title_ar : product.title} ${index + 1}`}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Product Info - مدمجة */}
      <div className="px-4 py-3 space-y-3">
        {/* Price */}
        <div className="bg-primary/10 rounded-xl p-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600 mb-1">{locale === 'ar' ? 'السعر' : 'Price'}</p>
              <div className="flex items-baseline space-x-1 rtl:space-x-reverse">
                <span className="text-xl font-bold text-primary">{product.price}</span>
                <span className="text-sm text-primary/70">{t('currency')}</span>
              </div>
            </div>
            <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
              <i className="ri-price-tag-3-line text-primary"></i>
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-gray-500">
            <span className="flex items-center space-x-1 rtl:space-x-reverse">
              <i className="ri-barcode-line"></i>
              <span>{locale === 'ar' ? 'الرمز:' : 'Code:'} {product.id}</span>
            </span>
            {category && (
              <span className="flex items-center space-x-1 rtl:space-x-reverse">
                <i className="ri-folder-line"></i>
                <span>{locale === 'ar' ? category.name_ar : category.name}</span>
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Content Tabs - مدمجة للهاتف */}
      <div className="px-4 py-3">
        <div className="space-y-2">
          {/* Tab Navigation */}
          <div className="flex space-x-1 rtl:space-x-reverse bg-gray-100 p-1 rounded-lg">
            {[
              { key: 'description', label: t('productDetails.description'), icon: 'ri-file-text-line' },
              { key: 'features', label: t('productDetails.features'), icon: 'ri-star-line' },
              { key: 'specs', label: t('productDetails.technicalSpecs'), icon: 'ri-settings-3-line' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`flex-1 flex items-center justify-center space-x-1 rtl:space-x-reverse px-2 py-2 rounded-md font-medium transition-all text-xs ${
                  activeTab === tab.key
                    ? 'bg-white text-primary shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <i className={`${tab.icon} text-sm`}></i>
                <span className="truncate">{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="min-h-[80px]">
            {/* Description */}
            {activeTab === 'description' && (product.description || product.description_ar) && (
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-700 leading-relaxed whitespace-pre-line">
                  {locale === 'ar' ? product.description_ar : product.description}
                </p>
              </div>
            )}

            {/* Features */}
            {activeTab === 'features' && product.features && product.features.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="space-y-2">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-start space-x-2 rtl:space-x-reverse">
                      <div className="flex-shrink-0 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                        <i className="ri-check-line text-white text-xs"></i>
                      </div>
                      <span className="text-sm text-gray-700">
                        {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Specifications */}
            {activeTab === 'specs' && product.specifications && product.specifications.length > 0 && (
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="space-y-2">
                  {product.specifications.map((spec, index) => (
                    <div key={index} className="flex items-center justify-between py-1 border-b border-gray-200 last:border-b-0">
                      <span className="font-medium text-gray-800 text-sm">
                        {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}
                      </span>
                      <span className="text-gray-600 text-sm">
                        {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Bottom Actions - ثابتة في الأسفل */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 space-y-3">
        {/* Quantity Selector */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">{t('productDetails.quantity')}</span>
          <div className="flex items-center bg-gray-100 rounded-lg">
            <button
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-primary"
            >
              <i className="ri-subtract-line"></i>
            </button>
            <div className="w-12 h-8 flex items-center justify-center border-x border-gray-200 bg-white">
              <span className="text-sm font-medium">{quantity}</span>
            </div>
            <button
              onClick={() => setQuantity(quantity + 1)}
              className="w-8 h-8 flex items-center justify-center text-gray-600 hover:text-primary"
            >
              <i className="ri-add-line"></i>
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={handleAddToCart}
            disabled={!product.is_available}
            className="bg-primary text-white py-3 px-4 rounded-xl font-semibold hover:bg-primary-dark transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 rtl:space-x-reverse"
          >
            <i className="ri-shopping-cart-line"></i>
            <span className="text-sm">{t('productDetails.addToCart')}</span>
          </button>
          
          <button
            onClick={onWhatsAppClick}
            className="bg-green-500 text-white py-3 px-4 rounded-xl font-semibold hover:bg-green-600 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
          >
            <i className="ri-whatsapp-line"></i>
            <span className="text-sm">{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
          </button>
        </div>
      </div>

      {/* Bottom Padding للتأكد من عدم تداخل المحتوى مع الأزرار الثابتة */}
      <div className="h-32"></div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-20 left-4 right-4 bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-xl shadow-lg z-50 animate-fade-in">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
              <i className="ri-check-line text-sm font-bold"></i>
            </div>
            <span className="font-semibold">{t('cartMessages.addedToCart')}</span>
          </div>
        </div>
      )}
    </div>
  );
}
